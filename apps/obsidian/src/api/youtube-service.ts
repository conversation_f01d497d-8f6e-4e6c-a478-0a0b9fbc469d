import { Notice } from "obsidian";
import { supabase } from "@/auth/supabase";
import { retry } from "@std/async";
import * as v from "valibot";
import type { YouTubeMetadata } from "./types/youtube";
import {
  YouTubeMetadataSchema,
  YouTubeAPIError as YouTubeAPIErrorClass,
  YouTubeNetworkError as YouTubeNetworkErrorClass,
  YouTubeAuthError as YouTubeAuthErrorClass,
  YouTubeNotFoundError as YouTubeNotFoundErrorClass,
  YouTubeProcessingError as YouTubeProcessingErrorClass,
  YouTubeValidationError as YouTubeValidationErrorClass,
} from "./types/youtube";

const API_HOST = process.env.API_HOST;
if (!API_HOST) {
  throw new Error("API_HOST is not set");
}

export class YouTubeApiService {
  private readonly RETRY_CONFIG = {
    maxAttempts: 3,
    minTimeout: 2000,
    maxTimeout: 16000,
    multiplier: 2,
  };

  /**
   * Fetches YouTube video metadata with progress notification
   * @param videoId - YouTube video ID
   * @returns Promise resolving to YouTube metadata or null if user is not logged in
   */
  async getVideoMetadata(videoId: string): Promise<YouTubeMetadata | null> {
    if (!videoId?.trim()) {
      throw new YouTubeAPIErrorClass("Video ID is required");
    }

    // Check if user is logged in
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error || !data.session?.access_token) {
        return null;
      }
    } catch {
      return null;
    }

    let progressNotice: Notice | undefined;

    using stack = new DisposableStack();
    // Set up progress notice timer (show after 2 seconds)
    const noticeTimer = window.setTimeout(() => {
      const notice = new Notice("Fetching YouTube metadata...", 0);
      stack.defer(() => notice.hide());
      progressNotice = notice;
    }, 2000);
    stack.defer(() => window.clearTimeout(noticeTimer));

    try {
      const metadata = await this.fetchWithRetry(videoId);

      if (progressNotice) {
        progressNotice.setMessage("YouTube metadata fetched successfully");
        await sleep(1500);
      }

      return metadata;
    } catch (error) {
      if (progressNotice) {
        const errorMessage = this.getErrorMessage(error);
        progressNotice.setMessage(
          `YouTube metadata fetch failed: ${errorMessage}`,
        );
        await sleep(3000);
      }

      throw error;
    }
  }

  /**
   * Fetches metadata with retry logic for 202 responses
   */
  private async fetchWithRetry(videoId: string): Promise<YouTubeMetadata> {
    // Retry **only** the request until the backend finishes processing.
    // Validation is performed after a successful response so that any schema
    // issues are reported immediately, instead of being retried pointlessly.
    const response = await retry(
      async () => {
        const response = await this.makeApiRequest(videoId);
        // 202 → still processing → retry.
        if (response.status === 202) {
          throw new YouTubeProcessingErrorClass(
            "Video is still being processed",
          );
        }
        return response;
      },
      {
        maxAttempts: this.RETRY_CONFIG.maxAttempts,
        minTimeout: this.RETRY_CONFIG.minTimeout,
        maxTimeout: this.RETRY_CONFIG.maxTimeout,
        multiplier: this.RETRY_CONFIG.multiplier,
        jitter: 0.1,
      },
    );
    // Convert non-OK responses into typed errors (no retry).
    if (!response.ok) {
      await this.handleErrorResponse(response, videoId);
    }

    // Validate once we have a definite result.
    return this.validateMetadata(await response.json());
  }

  /**
   * Makes the actual API request
   */
  private async makeApiRequest(videoId: string): Promise<Response> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await fetch(
        `${API_HOST}/youtube/video/${videoId}/metadata`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
          signal: AbortSignal.timeout(30000), // 30 second timeout
        },
      );

      return response;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === "AbortError") {
          throw new YouTubeNetworkErrorClass("Request timeout", error);
        }
        if (error.name === "TypeError" && error.message.includes("fetch")) {
          throw new YouTubeNetworkErrorClass(
            "Network connection failed",
            error,
          );
        }
      }
      throw new YouTubeNetworkErrorClass(
        "Network request failed",
        error as Error,
      );
    }
  }

  /**
   * Gets access token from Supabase auth
   */
  private async getAccessToken(): Promise<string> {
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        throw new YouTubeAuthErrorClass(
          `Authentication error: ${error.message}`,
        );
      }

      const accessToken = data.session?.access_token;
      if (!accessToken) {
        throw new YouTubeAuthErrorClass(
          "No access token available. Please log in.",
        );
      }

      return accessToken;
    } catch (error) {
      if (error instanceof YouTubeAuthErrorClass) {
        throw error;
      }
      throw new YouTubeAuthErrorClass("Failed to get authentication token");
    }
  }

  /**
   * Handles error responses from the API
   */
  private async handleErrorResponse(
    response: Response,
    videoId: string,
  ): Promise<never> {
    const status = response.status;

    try {
      const errorData = await response.json();
      const message =
        errorData.error || errorData.message || response.statusText;

      switch (status) {
        case 401:
        case 403:
          throw new YouTubeAuthErrorClass(
            `Authentication failed: ${message}`,
            status,
          );
        case 404:
          throw new YouTubeNotFoundErrorClass(videoId);
        case 429:
          throw new YouTubeAPIErrorClass(
            "Rate limit exceeded. Please try again later.",
            status,
          );
        case 500:
        case 502:
        case 503:
        case 504:
          throw new YouTubeAPIErrorClass(`Server error: ${message}`, status);
        default:
          throw new YouTubeAPIErrorClass(`API error: ${message}`, status);
      }
    } catch (parseError) {
      // If we can't parse the error response, use the status text
      switch (status) {
        case 401:
        case 403:
          throw new YouTubeAuthErrorClass("Authentication failed", status);
        case 404:
          throw new YouTubeNotFoundErrorClass(videoId);
        default:
          throw new YouTubeAPIErrorClass(
            `HTTP ${status}: ${response.statusText}`,
            status,
          );
      }
    }
  }

  /**
   * Validates the metadata response using Valibot schema
   */
  private validateMetadata(data: unknown): YouTubeMetadata {
    try {
      return v.parse(YouTubeMetadataSchema, data);
    } catch (error) {
      if (v.isValiError(error)) {
        const issues = v.flatten(error.issues).nested;
        const validationIssues = Object.entries(issues || {}).map(
          ([field, fieldIssues]) =>
            `${field}: ${fieldIssues?.join(", ") || "validation error"}`,
        );

        throw new YouTubeValidationErrorClass(
          "Invalid metadata response format",
          validationIssues,
        );
      }

      throw new YouTubeAPIErrorClass(
        `Metadata validation failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Extracts user-friendly error message
   */
  private getErrorMessage(error: unknown): string {
    if (error instanceof YouTubeAPIErrorClass) {
      return error.message;
    }
    if (error instanceof Error) {
      return error.message;
    }
    return "Unknown error occurred";
  }
}
